/**
 * @file MemorySafeResourceManagerEnhanced Comprehensive Test Suite
 * @component memory-safe-resource-manager-enhanced-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-001-memory-leak-prevention-testing
 * @task-id M-TSK-01.SUB-01.1.ENH-01
 * 
 * ENHANCED TESTING APPROACH:
 * - Jest fake timers for controlled timer execution
 * - Comprehensive coverage of all enhancement features
 * - Performance validation with <5ms operation requirements
 * - Backward compatibility verification
 * - Memory leak prevention validation
 */

import { 
  MemorySafeResourceManagerEnhanced,
  IResourcePoolConfig,
  IResourceScalingConfig,
  IReferenceTrackingConfig,
  IResourceLifecycleConfig,
  IResourceLifecycleEvent
} from '../MemorySafeResourceManagerEnhanced';
import { IResourceLimits } from '../MemorySafeResourceManager';

// ============================================================================
// TEST IMPLEMENTATION WITH ENHANCED FEATURES
// ============================================================================

class TestEnhancedResourceManager extends MemorySafeResourceManagerEnhanced {
  public initializeCalled = false;
  public shutdownCalled = false;
  public lifecycleEvents: IResourceLifecycleEvent[] = [];

  protected async doInitialize(): Promise<void> {
    this.initializeCalled = true;

    // Call parent doInitialize to set up enhanced features
    await super.doInitialize();

    // Set up lifecycle event capture for testing
    this.on('lifecycleEvent', (event: IResourceLifecycleEvent) => {
      this.lifecycleEvents.push(event);
    });
  }

  protected async doShutdown(): Promise<void> {
    this.shutdownCalled = true;
  }

  // Expose protected methods for testing
  public createTestResourcePool<T>(
    name: string,
    factory: () => T,
    cleanup: (resource: T) => void,
    config: IResourcePoolConfig
  ) {
    return this.createResourcePool(name, factory, cleanup, config);
  }

  public async borrowTestResource<T>(poolName: string): Promise<T> {
    return this.borrowFromPool<T>(poolName);
  }

  public async returnTestResource<T>(poolName: string, resource: T): Promise<void> {
    return this.returnToPool(poolName, resource);
  }

  public createTestAdvancedSharedResource<T>(
    factory: () => T,
    cleanup: (resource: T) => void,
    name?: string,
    config?: IReferenceTrackingConfig
  ) {
    return this.createAdvancedSharedResource(factory, cleanup, name, config);
  }

  public async initialize(): Promise<void> {
    return super.initialize();
  }

  public getTestMetrics() {
    return this.getEnhancedResourceMetrics();
  }

  public isTestHealthy(): boolean {
    return this.isEnhancedHealthy();
  }
}

describe('MemorySafeResourceManagerEnhanced', () => {
  let manager: TestEnhancedResourceManager;
  const customLimits: Partial<IResourceLimits> = {
    maxIntervals: 100,
    maxTimeouts: 200,
    maxCacheSize: 1000,
    cleanupIntervalMs: 60000
  };

  // Use Jest fake timers for controlled execution
  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  beforeEach(() => {
    manager = new TestEnhancedResourceManager(customLimits);
    (manager as any)._resources.clear();
    (manager as any)._isShuttingDown = false;
    (manager as any)._isInitialized = false;
    manager.lifecycleEvents = [];
  });

  afterEach(async () => {
    if (manager && !manager.isShuttingDown()) {
      await manager.shutdown();
    }
    jest.clearAllTimers();
  });

  // ============================================================================
  // BASIC ENHANCED FUNCTIONALITY
  // ============================================================================

  describe('Enhanced Initialization and Lifecycle', () => {
    it('should initialize with enhanced features enabled', async () => {
      await manager.initialize();
      
      expect(manager.initializeCalled).toBe(true);
      expect(manager.isTestHealthy()).toBe(true);
      
      const metrics = manager.getTestMetrics();
      expect(metrics.enhancementMetrics).toBeDefined();
      expect(metrics.poolMetrics).toBeDefined();
      expect(metrics.referenceMetrics).toBeDefined();
      expect(metrics.eventMetrics).toBeDefined();
    });

    it('should maintain backward compatibility with base class', async () => {
      await manager.initialize();
      
      // Test that base functionality still works
      const intervalId = (manager as any).createSafeInterval(() => {}, 1000, 'test');
      expect(intervalId).toBeDefined();
      
      const timeoutId = (manager as any).createSafeTimeout(() => {}, 1000, 'test');
      expect(timeoutId).toBeDefined();
      
      const baseMetrics = manager.getResourceMetrics();
      expect(baseMetrics.totalResources).toBeGreaterThan(0);
    });

    it('should shutdown enhanced features properly', async () => {
      await manager.initialize();
      await manager.shutdown();
      
      expect(manager.shutdownCalled).toBe(true);
      expect(manager.isShuttingDown()).toBe(true);
    });
  });

  // ============================================================================
  // RESOURCE POOL MANAGEMENT TESTS
  // ============================================================================

  describe('Resource Pool Management', () => {
    const poolConfig: IResourcePoolConfig = {
      minSize: 2,
      maxSize: 10,
      idleTimeoutMs: 5000,
      validationInterval: 1000,
      autoScale: true,
      scalingPolicy: 'adaptive'
    };

    it('should create resource pools efficiently', async () => {
      await manager.initialize();
      
      const startTime = Date.now();
      const pool = manager.createTestResourcePool(
        'test-pool',
        () => ({ id: Math.random(), data: 'test' }),
        (resource) => { /* cleanup */ },
        poolConfig
      );
      const duration = Date.now() - startTime;
      
      expect(pool).toBeDefined();
      expect(pool.size).toBe(poolConfig.minSize);
      expect(pool.available).toBe(poolConfig.minSize);
      expect(duration).toBeLessThan(5); // <5ms requirement
    });

    it('should borrow and return resources efficiently', async () => {
      await manager.initialize();
      
      const pool = manager.createTestResourcePool(
        'test-pool',
        () => ({ id: Math.random(), data: 'test' }),
        (resource) => { /* cleanup */ },
        poolConfig
      );
      
      // Test borrowing
      const startBorrow = Date.now();
      const resource1 = await manager.borrowTestResource<{ id: number; data: string }>('test-pool');
      const borrowDuration = Date.now() - startBorrow;

      expect(resource1).toBeDefined();
      expect(resource1.data).toBe('test');
      expect(borrowDuration).toBeLessThan(1); // <1ms for available resources
      expect(pool.available).toBe(poolConfig.minSize - 1);

      // Test returning
      const startReturn = Date.now();
      await manager.returnTestResource('test-pool', resource1);
      const returnDuration = Date.now() - startReturn;
      
      expect(returnDuration).toBeLessThan(1); // <0.5ms requirement
      expect(pool.available).toBe(poolConfig.minSize);
    });

    it('should handle pool exhaustion gracefully', async () => {
      await manager.initialize();
      
      const smallPoolConfig = { ...poolConfig, minSize: 1, maxSize: 2 };
      manager.createTestResourcePool(
        'small-pool',
        () => ({ id: Math.random() }),
        (resource) => { /* cleanup */ },
        smallPoolConfig
      );
      
      // Borrow all resources
      const resource1 = await manager.borrowTestResource<{ id: number }>('small-pool');
      const resource2 = await manager.borrowTestResource<{ id: number }>('small-pool');

      // Should throw when exhausted
      await expect(manager.borrowTestResource('small-pool')).rejects.toThrow('exhausted');

      // Should work again after returning
      await manager.returnTestResource('small-pool', resource1);
      const resource3 = await manager.borrowTestResource<{ id: number }>('small-pool');
      expect(resource3).toBeDefined();
    });

    it('should validate resources before returning to pool', async () => {
      await manager.initialize();
      
      let cleanupCalled = false;
      const pool = manager.createTestResourcePool(
        'validation-pool',
        () => ({ id: Math.random(), valid: true }),
        (resource) => { cleanupCalled = true; },
        poolConfig
      );
      
      const resource = await manager.borrowTestResource<{ id: number; valid: boolean }>('validation-pool');

      // Invalidate the resource
      resource.valid = false;
      
      // Mock the validator to reject invalid resources
      pool.validator = (res: any) => res.valid === true;
      
      await manager.returnTestResource('validation-pool', resource);
      
      // Should have cleaned up the invalid resource instead of returning it
      expect(cleanupCalled).toBe(true);
    });
  });

  // ============================================================================
  // DYNAMIC SCALING TESTS
  // ============================================================================

  describe('Dynamic Resource Scaling', () => {
    const scalingConfig: IResourceScalingConfig = {
      enabled: true,
      targetUtilization: 70,
      scaleUpThreshold: 85,
      scaleDownThreshold: 50,
      cooldownPeriod: 1000, // 1 second for testing
      maxScaleRate: 0.1,
      scalingPolicy: 'adaptive'
    };

    it('should enable dynamic scaling with proper configuration', async () => {
      await manager.initialize();
      
      manager.enableDynamicScaling(scalingConfig);
      
      // Should have created scaling interval
      const metrics = manager.getTestMetrics();
      expect(metrics.enhancementMetrics.scalingDecisions).toBe(0);
      
      // Check for lifecycle event
      const scalingEvents = manager.lifecycleEvents.filter(e => 
        e.resourceType === 'DynamicScaling' && e.type === 'created'
      );
      expect(scalingEvents.length).toBeGreaterThan(0);
    });

    it('should perform scaling analysis within performance bounds', async () => {
      await manager.initialize();
      manager.enableDynamicScaling(scalingConfig);
      
      // Create high utilization by creating many intervals
      for (let i = 0; i < 80; i++) {
        (manager as any).createSafeInterval(() => {}, 1000, `load-${i}`);
      }
      
      const startTime = Date.now();
      
      // Trigger scaling analysis by advancing timers
      jest.advanceTimersByTime(30000);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(100); // <100ms requirement
      
      const metrics = manager.getTestMetrics();
      expect(metrics.enhancementMetrics.scalingDecisions).toBeGreaterThan(0);
    });

    it('should respect cooldown periods', async () => {
      await manager.initialize();
      manager.enableDynamicScaling({ ...scalingConfig, cooldownPeriod: 60000 }); // 1 minute
      
      // Create high load
      for (let i = 0; i < 90; i++) {
        (manager as any).createSafeInterval(() => {}, 1000, `load-${i}`);
      }
      
      // First scaling analysis
      jest.advanceTimersByTime(30000);
      const firstDecisions = manager.getTestMetrics().enhancementMetrics.scalingDecisions;
      
      // Second analysis within cooldown
      jest.advanceTimersByTime(30000);
      const secondDecisions = manager.getTestMetrics().enhancementMetrics.scalingDecisions;
      
      // Should not have made additional scaling decisions due to cooldown
      expect(secondDecisions).toBe(firstDecisions);
    });
  });

  // ============================================================================
  // ENHANCED REFERENCE COUNTING TESTS
  // ============================================================================

  describe('Enhanced Reference Counting', () => {
    const refConfig: IReferenceTrackingConfig = {
      enableWeakReferences: true,
      autoCleanupIdleResources: true,
      idleThresholdMs: 5000,
      trackAccessPatterns: true,
      maxAccessHistory: 100
    };

    it('should create advanced shared resources with enhanced tracking', async () => {
      await manager.initialize();

      let cleanupCalled = false;
      const startTime = Date.now();

      const { resource, addRef, releaseRef, addWeakRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'test-resource', id: Math.random() }),
        (res) => { cleanupCalled = true; },
        'test-advanced-resource',
        refConfig
      );

      const duration = Date.now() - startTime;

      expect(resource).toBeDefined();
      expect(resource.data).toBe('test-resource');
      expect(duration).toBeLessThan(1); // <1ms requirement
      expect(addRef).toBeInstanceOf(Function);
      expect(releaseRef).toBeInstanceOf(Function);
      expect(addWeakRef).toBeInstanceOf(Function);
      expect(cleanupCalled).toBe(false);
    });

    it('should handle strong reference counting correctly', async () => {
      await manager.initialize();

      let cleanupCalled = false;
      const { resource, addRef, releaseRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'test' }),
        (res) => { cleanupCalled = true; },
        'ref-test'
      );

      // Add additional strong references
      const ref1 = addRef();
      const ref2 = addRef();

      expect(ref1).toBeDefined();
      expect(ref2).toBeDefined();
      expect(ref1).not.toBe(ref2);

      // Release references one by one
      releaseRef(ref1);
      expect(cleanupCalled).toBe(false); // Still has references

      releaseRef(ref2);
      expect(cleanupCalled).toBe(false); // Still has original reference

      // This should trigger cleanup (original reference is released automatically on zero refs)
      // We need to simulate the original reference release
      const metrics = manager.getTestMetrics();
      expect(metrics.referenceMetrics.advancedRefCount).toBeGreaterThan(0);
    });

    it('should handle weak references without preventing cleanup', async () => {
      await manager.initialize();

      let cleanupCalled = false;
      const { resource, addWeakRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'test' }),
        (res) => { cleanupCalled = true; },
        'weak-ref-test'
      );

      // Add weak references
      const weakRef1 = addWeakRef();
      const weakRef2 = addWeakRef();

      expect(weakRef1).toBeDefined();
      expect(weakRef2).toBeDefined();

      const metrics = manager.getTestMetrics();
      expect(metrics.referenceMetrics.weakRefCount).toBe(2);

      // Weak references should not prevent cleanup when strong refs are gone
      // (This would be tested in integration with the actual cleanup mechanism)
    });

    it('should track access patterns and metadata', async () => {
      await manager.initialize();

      const { resource, addRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'test' }),
        (res) => { /* cleanup */ },
        'access-tracking-test',
        refConfig
      );

      // Add multiple references to track access
      addRef();
      addRef();

      const metrics = manager.getTestMetrics();
      expect(metrics.referenceMetrics.totalRefCount).toBeGreaterThan(1);

      // Check that access patterns are being tracked
      const enhancedMetrics = metrics.enhancementMetrics;
      expect(enhancedMetrics.referenceOperations).toBeGreaterThan(0);
    });

    it('should perform reference operations within performance bounds', async () => {
      await manager.initialize();

      const { addRef, releaseRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'performance-test' }),
        (res) => { /* cleanup */ },
        'performance-ref-test'
      );

      // Test addRef performance
      const startAdd = Date.now();
      const ref = addRef();
      const addDuration = Date.now() - startAdd;

      expect(addDuration).toBeLessThan(1); // <1ms requirement

      // Test releaseRef performance
      const startRelease = Date.now();
      releaseRef(ref);
      const releaseDuration = Date.now() - startRelease;

      expect(releaseDuration).toBeLessThan(1); // <1ms requirement
    });
  });

  // ============================================================================
  // RESOURCE LIFECYCLE EVENTS TESTS
  // ============================================================================

  describe('Resource Lifecycle Events', () => {
    const lifecycleConfig: IResourceLifecycleConfig = {
      enableEvents: true,
      eventBufferSize: 10,
      emitInterval: 1000,
      enabledEvents: new Set(['created', 'accessed', 'cleanup', 'error']),
      eventHandlers: new Map()
    };

    it('should enable lifecycle events with proper configuration', async () => {
      await manager.initialize();

      manager.enableResourceLifecycleEvents(lifecycleConfig);

      // Should have emitted configuration event
      const configEvents = manager.lifecycleEvents.filter(e =>
        e.resourceType === 'LifecycleEvents' && e.type === 'created'
      );
      expect(configEvents.length).toBeGreaterThan(0);
    });

    it('should emit events for resource operations', async () => {
      await manager.initialize();
      manager.enableResourceLifecycleEvents(lifecycleConfig);

      // Clear initial events
      manager.lifecycleEvents = [];

      // Create a resource pool (should emit events)
      manager.createTestResourcePool(
        'event-test-pool',
        () => ({ id: Math.random() }),
        (res) => { /* cleanup */ },
        {
          minSize: 1,
          maxSize: 5,
          idleTimeoutMs: 5000,
          validationInterval: 1000,
          autoScale: false,
          scalingPolicy: 'conservative'
        }
      );

      // Should have emitted pool creation event
      const poolEvents = manager.lifecycleEvents.filter(e =>
        e.resourceType === 'ResourcePool' && e.type === 'pooled'
      );
      expect(poolEvents.length).toBeGreaterThan(0);

      // Verify event structure
      const event = poolEvents[0];
      expect(event.resourceId).toBe('event-test-pool');
      expect(event.timestamp).toBeInstanceOf(Date);
      expect(event.component).toBe('MemorySafeResourceManagerEnhanced');
      expect(event.metadata).toBeDefined();
    });

    it('should buffer and flush events efficiently', async () => {
      await manager.initialize();

      const smallBufferConfig = { ...lifecycleConfig, eventBufferSize: 3 };
      manager.enableResourceLifecycleEvents(smallBufferConfig);

      let batchEventReceived = false;
      manager.on('lifecycleEventsBatch', (batch) => {
        batchEventReceived = true;
        expect(batch.events).toBeInstanceOf(Array);
        expect(batch.batchSize).toBeGreaterThan(0);
      });

      // Clear initial events
      manager.lifecycleEvents = [];

      // Create multiple operations to trigger buffer flush
      for (let i = 0; i < 5; i++) {
        manager.createTestResourcePool(
          `batch-test-pool-${i}`,
          () => ({ id: i }),
          (res) => { /* cleanup */ },
          {
            minSize: 1,
            maxSize: 2,
            idleTimeoutMs: 5000,
            validationInterval: 1000,
            autoScale: false,
            scalingPolicy: 'conservative'
          }
        );
      }

      // Should have triggered batch event due to buffer overflow
      expect(batchEventReceived).toBe(true);
    });

    it('should emit events within performance bounds', async () => {
      await manager.initialize();
      manager.enableResourceLifecycleEvents(lifecycleConfig);

      // Clear initial events
      manager.lifecycleEvents = [];

      const startTime = Date.now();

      // Perform operation that emits events
      manager.createTestResourcePool(
        'performance-event-pool',
        () => ({ id: Math.random() }),
        (res) => { /* cleanup */ },
        {
          minSize: 1,
          maxSize: 5,
          idleTimeoutMs: 5000,
          validationInterval: 1000,
          autoScale: false,
          scalingPolicy: 'conservative'
        }
      );

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5); // Should include event emission overhead

      // Verify events were emitted
      expect(manager.lifecycleEvents.length).toBeGreaterThan(0);
    });

    it('should handle event handler errors gracefully', async () => {
      await manager.initialize();

      const errorConfig = { ...lifecycleConfig };
      errorConfig.eventHandlers.set('created', () => {
        throw new Error('Test handler error');
      });

      manager.enableResourceLifecycleEvents(errorConfig);

      // Should not throw despite handler error
      expect(() => {
        manager.createTestResourcePool(
          'error-handler-pool',
          () => ({ id: Math.random() }),
          (res) => { /* cleanup */ },
          {
            minSize: 1,
            maxSize: 2,
            idleTimeoutMs: 5000,
            validationInterval: 1000,
            autoScale: false,
            scalingPolicy: 'conservative'
          }
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // PERFORMANCE VALIDATION TESTS
  // ============================================================================

  describe('Performance Validation', () => {
    it('should maintain 0% overhead in test mode', async () => {
      // Verify we're in test mode
      expect(process.env.NODE_ENV).toBe('test');

      await manager.initialize();

      const metrics = manager.getTestMetrics();

      // In test mode, memory usage should be minimal
      expect(metrics.memoryUsageMB).toBeLessThan(1); // <1MB in test mode

      // Performance operations should be fast
      const startTime = Date.now();

      // Perform multiple operations
      const pool = manager.createTestResourcePool(
        'perf-test-pool',
        () => ({ id: Math.random() }),
        (res) => { /* cleanup */ },
        {
          minSize: 2,
          maxSize: 10,
          idleTimeoutMs: 5000,
          validationInterval: 1000,
          autoScale: true,
          scalingPolicy: 'adaptive'
        }
      );

      const resource = await manager.borrowTestResource('perf-test-pool');
      await manager.returnTestResource('perf-test-pool', resource);

      const { addRef, releaseRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'perf-test' }),
        (res) => { /* cleanup */ },
        'perf-shared-resource'
      );

      const ref = addRef();
      releaseRef(ref);

      const totalDuration = Date.now() - startTime;
      expect(totalDuration).toBeLessThan(10); // All operations <10ms total
    });

    it('should meet individual operation performance requirements', async () => {
      await manager.initialize();

      // Resource pool operations <5ms
      const poolStart = Date.now();
      const pool = manager.createTestResourcePool(
        'timing-pool',
        () => ({ id: Math.random() }),
        (res) => { /* cleanup */ },
        {
          minSize: 1,
          maxSize: 5,
          idleTimeoutMs: 5000,
          validationInterval: 1000,
          autoScale: false,
          scalingPolicy: 'conservative'
        }
      );
      const poolDuration = Date.now() - poolStart;
      expect(poolDuration).toBeLessThan(5);

      // Borrow operation <1ms for available resources
      const borrowStart = Date.now();
      const resource = await manager.borrowTestResource('timing-pool');
      const borrowDuration = Date.now() - borrowStart;
      expect(borrowDuration).toBeLessThan(1);

      // Return operation <0.5ms
      const returnStart = Date.now();
      await manager.returnTestResource('timing-pool', resource);
      const returnDuration = Date.now() - returnStart;
      expect(returnDuration).toBeLessThan(1); // Relaxed to 1ms for test environment

      // Reference operations <1ms
      const refStart = Date.now();
      const { addRef, releaseRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'timing-test' }),
        (res) => { /* cleanup */ },
        'timing-ref'
      );
      const ref = addRef();
      releaseRef(ref);
      const refDuration = Date.now() - refStart;
      expect(refDuration).toBeLessThan(1);
    });

    it('should handle high-volume operations efficiently', async () => {
      await manager.initialize();

      const startTime = Date.now();
      const operationCount = 100;

      // Create multiple pools
      for (let i = 0; i < 10; i++) {
        manager.createTestResourcePool(
          `volume-pool-${i}`,
          () => ({ id: i, data: `test-${i}` }),
          (res) => { /* cleanup */ },
          {
            minSize: 1,
            maxSize: 3,
            idleTimeoutMs: 5000,
            validationInterval: 1000,
            autoScale: false,
            scalingPolicy: 'conservative'
          }
        );
      }

      // Perform many reference operations
      const refs: Array<{ addRef: () => string; releaseRef: (id: string) => void }> = [];
      for (let i = 0; i < 20; i++) {
        const ref = manager.createTestAdvancedSharedResource(
          () => ({ data: `volume-test-${i}` }),
          (res) => { /* cleanup */ },
          `volume-ref-${i}`
        );
        refs.push(ref);
      }

      // Add and release references
      const refIds: string[] = [];
      refs.forEach(ref => {
        refIds.push(ref.addRef());
      });

      refs.forEach((ref, index) => {
        ref.releaseRef(refIds[index]);
      });

      const totalDuration = Date.now() - startTime;
      const avgOperationTime = totalDuration / operationCount;

      expect(avgOperationTime).toBeLessThan(1); // Average <1ms per operation
      expect(totalDuration).toBeLessThan(100); // Total <100ms for all operations
    });
  });

  // ============================================================================
  // BACKWARD COMPATIBILITY TESTS
  // ============================================================================

  describe('Backward Compatibility', () => {
    it('should preserve all base class functionality', async () => {
      await manager.initialize();

      // Test base interval creation
      const intervalId = (manager as any).createSafeInterval(() => {}, 1000, 'compat-test');
      expect(intervalId).toBeDefined();
      expect(typeof intervalId).toBe('string');

      // Test base timeout creation
      const timeoutId = (manager as any).createSafeTimeout(() => {}, 1000, 'compat-test');
      expect(timeoutId).toBeDefined();
      expect(typeof timeoutId).toBe('string');

      // Test base shared resource creation
      const { resource, releaseRef } = (manager as any).createSharedResource(
        () => ({ data: 'compat-test' }),
        (res: any) => { /* cleanup */ },
        'compat-shared'
      );
      expect(resource).toBeDefined();
      expect(resource.data).toBe('compat-test');
      expect(releaseRef).toBeInstanceOf(Function);

      // Test base metrics
      const baseMetrics = manager.getResourceMetrics();
      expect(baseMetrics.totalResources).toBeGreaterThan(0);
      expect(baseMetrics.activeIntervals).toBeGreaterThan(0);
      expect(baseMetrics.activeTimeouts).toBeGreaterThan(0);

      // Test base health check
      expect(manager.isHealthy()).toBe(true);
    });

    it('should maintain base class event emission', async () => {
      await manager.initialize();

      let resourceCreatedEvents = 0;
      let resourceCleanedEvents = 0;

      manager.on('resourceCreated', () => {
        resourceCreatedEvents++;
      });

      manager.on('resourceCleaned', () => {
        resourceCleanedEvents++;
      });

      // Create and cleanup base resources
      const intervalId = (manager as any).createSafeInterval(() => {}, 1000, 'event-test');
      await (manager as any)._cleanupResource(intervalId);

      expect(resourceCreatedEvents).toBeGreaterThan(0);
      expect(resourceCleanedEvents).toBeGreaterThan(0);
    });

    it('should preserve base class error handling', async () => {
      await manager.initialize();

      let errorEvents = 0;
      manager.on('error', () => {
        errorEvents++;
      });

      // Test error handling in base functionality
      expect(() => {
        (manager as any).createSharedResource(
          () => { throw new Error('Test factory error'); },
          (res: any) => { /* cleanup */ },
          'error-test'
        );
      }).toThrow('Test factory error');

      // Error event should have been emitted
      expect(errorEvents).toBeGreaterThan(0);
    });

    it('should maintain base class resource limits', async () => {
      await manager.initialize();

      // Test that base limits are still enforced
      const metrics = manager.getResourceMetrics();
      expect(metrics.totalResources).toBeLessThan(customLimits.maxIntervals! + customLimits.maxTimeouts!);

      // Enhanced limits should be based on base limits
      const enhancedMetrics = manager.getTestMetrics();
      expect(enhancedMetrics.totalResources).toBe(metrics.totalResources);
    });
  });

  // ============================================================================
  // INTEGRATION AND HEALTH TESTS
  // ============================================================================

  describe('Integration and Health', () => {
    it('should integrate enhanced features seamlessly', async () => {
      await manager.initialize();

      // Enable all enhanced features
      manager.enableDynamicScaling({
        enabled: true,
        targetUtilization: 70,
        scaleUpThreshold: 85,
        scaleDownThreshold: 50,
        cooldownPeriod: 5000,
        maxScaleRate: 0.1,
        scalingPolicy: 'adaptive'
      });

      manager.enableResourceLifecycleEvents({
        enableEvents: true,
        eventBufferSize: 20,
        emitInterval: 2000,
        enabledEvents: new Set(['created', 'accessed', 'cleanup']),
        eventHandlers: new Map()
      });

      // Create resources using both base and enhanced features
      const intervalId = (manager as any).createSafeInterval(() => {}, 1000, 'integration-test');

      const pool = manager.createTestResourcePool(
        'integration-pool',
        () => ({ id: Math.random() }),
        (res) => { /* cleanup */ },
        {
          minSize: 2,
          maxSize: 8,
          idleTimeoutMs: 5000,
          validationInterval: 1000,
          autoScale: true,
          scalingPolicy: 'adaptive'
        }
      );

      const { resource, addRef } = manager.createTestAdvancedSharedResource(
        () => ({ data: 'integration-test' }),
        (res) => { /* cleanup */ },
        'integration-shared'
      );

      // All features should work together
      expect(manager.isTestHealthy()).toBe(true);
      expect(manager.isHealthy()).toBe(true);

      const metrics = manager.getTestMetrics();
      expect(metrics.enhancementMetrics.poolOperations).toBeGreaterThan(0);
      expect(metrics.enhancementMetrics.referenceOperations).toBeGreaterThan(0);
      expect(metrics.enhancementMetrics.eventsEmitted).toBeGreaterThan(0);
    });

    it('should handle resource optimization cycles', async () => {
      await manager.initialize();

      // Create resources that will need optimization
      const pool = manager.createTestResourcePool(
        'optimization-pool',
        () => ({ id: Math.random(), created: Date.now() }),
        (res) => { /* cleanup */ },
        {
          minSize: 1,
          maxSize: 10,
          idleTimeoutMs: 1000, // Short timeout for testing
          validationInterval: 500,
          autoScale: true,
          scalingPolicy: 'adaptive'
        }
      );

      // Borrow and return resources to create optimization opportunities
      const resources = [];
      for (let i = 0; i < 5; i++) {
        resources.push(await manager.borrowTestResource('optimization-pool'));
      }

      for (const resource of resources) {
        await manager.returnTestResource('optimization-pool', resource);
      }

      // Trigger optimization cycle
      jest.advanceTimersByTime(60000); // 1 minute

      // Should still be healthy after optimization
      expect(manager.isTestHealthy()).toBe(true);

      const metrics = manager.getTestMetrics();
      expect(metrics.enhancementMetrics.lastOptimization).toBeInstanceOf(Date);
    });

    it('should maintain health under stress conditions', async () => {
      await manager.initialize();

      // Create stress conditions
      for (let i = 0; i < 20; i++) {
        (manager as any).createSafeInterval(() => {}, 1000, `stress-${i}`);
      }

      for (let i = 0; i < 10; i++) {
        manager.createTestResourcePool(
          `stress-pool-${i}`,
          () => ({ id: i }),
          (res) => { /* cleanup */ },
          {
            minSize: 1,
            maxSize: 3,
            idleTimeoutMs: 5000,
            validationInterval: 1000,
            autoScale: false,
            scalingPolicy: 'conservative'
          }
        );
      }

      for (let i = 0; i < 15; i++) {
        manager.createTestAdvancedSharedResource(
          () => ({ data: `stress-${i}` }),
          (res) => { /* cleanup */ },
          `stress-ref-${i}`
        );
      }

      // Should remain healthy under stress
      expect(manager.isTestHealthy()).toBe(true);
      expect(manager.isHealthy()).toBe(true);

      const metrics = manager.getTestMetrics();
      expect(metrics.totalResources).toBeGreaterThan(20);
      expect(metrics.poolMetrics.poolCount).toBe(10);
      expect(metrics.referenceMetrics.advancedRefCount).toBe(15);
    });
  });
});
